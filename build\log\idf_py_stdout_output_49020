-- Existing sdkconfig 'D:/CodeBase/Embedded/ESP32/hello_world/sdkconfig' renamed to 'D:/CodeBase/Embedded/ESP32/hello_world/sdkconfig.old'.
-- Found Git: D:/software/ESP-IDF/Espressif/tools/idf-git/2.44.0/cmd/git.exe (found version "2.44.0.windows.1")
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file D:/CodeBase/Embedded/ESP32/hello_world/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: D:/software/ESP-IDF/Espressif/python_env/idf5.3_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "hello_world" version: 1
-- Adding linker script D:/CodeBase/Embedded/ESP32/hello_world/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/CodeBase/Embedded/ESP32/hello_world/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/app_trace D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/app_update D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader_support D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/bt D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/cmock D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/console D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/cxx D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/driver D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/efuse D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp-tls D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_app_format D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_bootloader_format D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_coex D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_common D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ana_cmpr D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_cam D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_dac D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gpio D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gptimer D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2c D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2s D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_isp D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_jpeg D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ledc D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_mcpwm D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_parlio D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_pcnt D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ppa D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_rmt D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdio D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdm D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdmmc D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdspi D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_spi D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_touch_sens D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_tsens D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_uart D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_usb_serial_jtag D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_eth D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_event D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_gdbstub D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hid D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_client D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_server D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_ota D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_server D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_lcd D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_local_ctrl D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_mm D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif_stack D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_partition D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_phy D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_pm D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_psram D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_ringbuf D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_timer D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_vfs_console D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/espcoredump D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/fatfs D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/freertos D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/hal D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/heap D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/http_parser D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/idf_test D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/ieee802154 D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/json D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/log D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/lwip D:/CodeBase/Embedded/ESP32/hello_world/main D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/mqtt D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/newlib D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_flash D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_sec_provider D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/openthread D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/perfmon D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/protobuf-c D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/pthread D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/sdmmc D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/soc D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/spi_flash D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/spiffs D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/tcp_transport D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/touch_element D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/ulp D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/unity D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/usb D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/vfs D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/wear_levelling D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/wifi_provisioning D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/xtensa
-- Configuring done (12.0s)
-- Generating done (0.9s)
-- Build files have been written to: D:/CodeBase/Embedded/ESP32/hello_world/build
