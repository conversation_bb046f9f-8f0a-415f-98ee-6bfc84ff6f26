set(CMAKE_ASM_COMPILER "D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-ar.exe")
set(CMAKE_ASM_COMPILER_AR "D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc-ar.exe")
set(CMAKE_RANLIB "D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-ranlib.exe")
set(CMAKE_ASM_COMPILER_RANLIB "D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc-ranlib.exe")
set(CMAKE_LINKER "D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-ld.exe")
set(CMAKE_LINKER_LINK "")
set(CMAKE_LINKER_LLD "")
set(CMAKE_ASM_COMPILER_LINKER "")
set(CMAKE_ASM_COMPILER_LINKER_ID "")
set(CMAKE_ASM_COMPILER_LINKER_VERSION )
set(CMAKE_ASM_COMPILER_LINKER_FRONTEND_VARIANT )
set(CMAKE_MT "")
set(CMAKE_TAPI "CMAKE_TAPI-NOTFOUND")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)
set(CMAKE_ASM_LINKER_DEPFILE_SUPPORTED )


