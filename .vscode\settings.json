{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/", "idf.toolsPathWin": "D:\\software\\ESP-IDF\\Espressif", "idf.pythonInstallPath": "D:\\software\\ESP-IDF\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.portWin": "COM4", "idf.flashType": "UART", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "D:\\software\\ESP-IDF\\Espressif\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=D:\\software\\ESP-IDF\\Espressif\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=d:\\CodeBase\\Embedded\\ESP32\\hello_world\\build"]}