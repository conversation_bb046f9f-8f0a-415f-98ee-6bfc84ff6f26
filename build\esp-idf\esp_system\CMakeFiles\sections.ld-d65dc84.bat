@echo off
cd /D D:\CodeBase\Embedded\ESP32\hello_world\build\esp-idf\esp_system || (set FAIL_LINE=2& goto :ABORT)
D:\software\ESP-IDF\Espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/tools/ldgen/ldgen.py --config D:/CodeBase/Embedded/ESP32/hello_world/sdkconfig --fragments-list D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/xtensa/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gpio/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_pm/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_mm/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/spi_flash/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system/app.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_common/common.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_common/soc.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/hal/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/log/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/heap/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/soc/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/dma/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/ldo/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/linker_common.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/newlib/newlib.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/newlib/system_libs.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gptimer/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_ringbuf/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_uart/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/app_trace/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_event/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_pcnt/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_spi/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_mcpwm/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ana_cmpr/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_dac/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_rmt/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdm/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2c/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ledc/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_parlio/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/driver/twai/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_phy/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/vfs/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_gdbstub/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_eth/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_psram/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/esp_lcd/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/espcoredump/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/ieee802154/linker.lf;D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/components/openthread/linker.lf --input D:/CodeBase/Embedded/ESP32/hello_world/build/esp-idf/esp_system/ld/sections.ld.in --output D:/CodeBase/Embedded/ESP32/hello_world/build/esp-idf/esp_system/ld/sections.ld --kconfig D:/software/ESP-IDF/Espressif/frameworks/esp-idf-v5.3.3/Kconfig --env-file D:/CodeBase/Embedded/ESP32/hello_world/build/config.env --libraries-file D:/CodeBase/Embedded/ESP32/hello_world/build/ldgen_libraries --objdump D:/software/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-objdump.exe || (set FAIL_LINE=3& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%